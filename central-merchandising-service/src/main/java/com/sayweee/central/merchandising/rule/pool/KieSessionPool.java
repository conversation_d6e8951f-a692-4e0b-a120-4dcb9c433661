package com.sayweee.central.merchandising.rule.pool;

import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * KieSession对象池
 * 用于管理和复用KieSession实例，提升性能
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
@Component
@Slf4j
public class KieSessionPool {

    @Autowired
    private KieContainer kieContainer;

    private final Queue<KieSession> sessionPool = new ConcurrentLinkedQueue<>();
    private final AtomicInteger activeCount = new AtomicInteger(0);
    private final AtomicInteger totalCreated = new AtomicInteger(0);
    private final AtomicInteger failedValidations = new AtomicInteger(0);
    
    // 用于创建session时的双重检查锁定
    private final ReentrantLock createLock = new ReentrantLock();
    
    // 标记池是否已关闭
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);

    @Value("${drools.session.pool.max-size:10}")
    private int maxPoolSize;

    @Value("${drools.session.pool.min-idle:2}")
    private int minIdleSize;
    
    @Value("${drools.session.pool.validate-on-borrow:true}")
    private boolean validateOnBorrow;
    
    @Value("${drools.session.pool.max-wait-millis:5000}")
    private long maxWaitMillis;

    @PostConstruct
    public void init() {
        log.info("初始化KieSession池，最大池大小: {}, 最小空闲数: {}", maxPoolSize, minIdleSize);
        
        // 预创建最小数量的session
        for (int i = 0; i < minIdleSize; i++) {
            try {
                KieSession session = createNewSession();
                if (session != null) {
                    sessionPool.offer(session);
                }
            } catch (Exception e) {
                log.warn("预创建KieSession失败: {}", e.getMessage());
            }
        }
        
        log.info("KieSession池初始化完成，当前池大小: {}", sessionPool.size());
    }

    /**
     * 从池中借用一个KieSession（增强版）
     * 支持超时等待、会话验证和线程安全创建
     *
     * @return KieSession实例，如果池为空且未达到最大限制则创建新实例
     * @throws IllegalStateException 如果池已关闭或无法获取session
     */
    public KieSession borrowSession() {
        if (isShutdown.get()) {
            throw new IllegalStateException("KieSession池已关闭");
        }
        
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < maxWaitMillis) {
            // 尝试从池中获取
            KieSession session = sessionPool.poll();
            
            if (session != null) {
                // 验证session是否有效
                if (validateOnBorrow && !isSessionValid(session)) {
                    log.warn("从池中获取的KieSession无效，将销毁并重新创建");
                    disposeSession(session);
                    failedValidations.incrementAndGet();
                    session = null;
                }
                
                if (session != null) {
                    activeCount.incrementAndGet();
                    log.debug("借用KieSession，当前活跃数量: {}, 池大小: {}", 
                        activeCount.get(), sessionPool.size());
                    return session;
                }
            }
            
            // 如果池为空，尝试创建新session
            if (activeCount.get() < maxPoolSize) {
                // 使用双重检查锁定模式确保线程安全
                createLock.lock();
                try {
                    // 再次检查，防止在等待锁期间其他线程已创建
                    if (activeCount.get() < maxPoolSize) {
                        session = createNewSession();
                        if (session != null) {
                            activeCount.incrementAndGet();
                            log.debug("创建新KieSession，当前活跃数量: {}", activeCount.get());
                            return session;
                        }
                    }
                } finally {
                    createLock.unlock();
                }
            }
            
            // 短暂等待，避免忙等待
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("等待KieSession时被中断", e);
            }
        }
        
        log.error("无法在{}ms内获取KieSession，池已满或创建失败", maxWaitMillis);
        throw new IllegalStateException("无法获取KieSession：超时");
    }
    
    /**
     * 验证KieSession是否有效
     */
    private boolean isSessionValid(KieSession session) {
        if (session == null) {
            return false;
        }
        
        try {
            // 尝试执行一个简单操作来验证session
            session.getGlobals();
            return true;
        } catch (Exception e) {
            log.debug("KieSession验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全地销毁session
     */
    private void disposeSession(KieSession session) {
        if (session != null) {
            try {
                session.dispose();
            } catch (Exception e) {
                log.warn("销毁KieSession时发生异常: {}", e.getMessage());
            }
        }
    }

    /**
     * 将KieSession归还到池中（增强版）
     * 包含池大小控制和状态验证
     *
     * @param session 要归还的KieSession
     */
    public void returnSession(KieSession session) {
        if (session == null) {
            return;
        }
        
        // 如果池已关闭，直接销毁session
        if (isShutdown.get()) {
            activeCount.decrementAndGet();
            disposeSession(session);
            return;
        }
        
        try {
            // 清理session状态
            cleanupSession(session);
            
            // 检查池大小，避免无限增长
            if (sessionPool.size() >= maxPoolSize) {
                log.debug("池已满，销毁KieSession而不是归还");
                disposeSession(session);
            } else {
                // 归还到池中
                sessionPool.offer(session);
                log.debug("归还KieSession，当前活跃数量: {}, 池大小: {}", 
                    activeCount.get(), sessionPool.size());
            }
        } catch (Exception e) {
            log.warn("归还KieSession时发生异常，销毁该session: {}", e.getMessage());
            disposeSession(session);
        } finally {
            activeCount.decrementAndGet();
        }
    }

    /**
     * 创建新的KieSession
     *
     * @return 新的KieSession实例
     */
    private KieSession createNewSession() {
        try {
            if (kieContainer == null) {
                log.error("KieContainer为空，无法创建KieSession");
                return null;
            }
            
            KieSession session = kieContainer.newKieSession();
            totalCreated.incrementAndGet();
            log.debug("创建新的KieSession，总创建数: {}", totalCreated.get());
            return session;
        } catch (Exception e) {
            log.error("创建KieSession失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清理KieSession状态（增强版）
     * 更安全的清理逻辑
     *
     * @param session 要清理的KieSession
     */
    private void cleanupSession(KieSession session) {
        try {
            // 清除所有fact handles
            session.getFactHandles().forEach(handle -> {
                try {
                    session.delete(handle);
                } catch (Exception e) {
                    log.debug("删除fact handle失败: {}", e.getMessage());
                }
            });
            
            // 清除全局变量
            session.getGlobals().getGlobalKeys().forEach(key -> {
                try {
                    session.setGlobal(key, null);
                } catch (Exception e) {
                    log.debug("清理全局变量失败: {}", key);
                }
            });
            
            // 触发垃圾回收规则（如果存在）
            try {
                session.fireAllRules();
            } catch (Exception e) {
                log.debug("触发清理规则失败: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.warn("清理KieSession状态时发生异常: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 获取池的统计信息（增强版）
     */
    public PoolStats getStats() {
        return new PoolStats(
            activeCount.get(),
            sessionPool.size(),
            totalCreated.get(),
            maxPoolSize,
            failedValidations.get(),
            isShutdown.get()
        );
    }
    
    /**
     * 执行池维护（可由定时任务调用）
     */
    public void performMaintenance() {
        if (isShutdown.get()) {
            return;
        }
        
        log.debug("开始执行池维护");
        
        // 确保最小空闲数
        int currentIdle = sessionPool.size();
        if (currentIdle < minIdleSize) {
            int toCreate = minIdleSize - currentIdle;
            for (int i = 0; i < toCreate; i++) {
                if (activeCount.get() + sessionPool.size() < maxPoolSize) {
                    KieSession session = createNewSession();
                    if (session != null) {
                        sessionPool.offer(session);
                    }
                }
            }
        }
        
        // 清理过多的空闲session
        while (sessionPool.size() > maxPoolSize) {
            KieSession session = sessionPool.poll();
            if (session != null) {
                disposeSession(session);
            }
        }
        
        log.debug("池维护完成，当前池大小: {}", sessionPool.size());
    }

    @PreDestroy
    public void destroy() {
        log.info("开始销毁KieSession池");
        
        // 标记池已关闭
        isShutdown.set(true);
        
        // 等待所有活跃session归还（最多等待10秒）
        long shutdownStart = System.currentTimeMillis();
        while (activeCount.get() > 0 && 
               System.currentTimeMillis() - shutdownStart < 10000) {
            log.info("等待{}个活跃session归还...", activeCount.get());
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        if (activeCount.get() > 0) {
            log.warn("强制关闭，仍有{}个活跃session未归还", activeCount.get());
        }
        
        // 清理池中的所有session
        int cleaned = 0;
        while (!sessionPool.isEmpty()) {
            KieSession session = sessionPool.poll();
            if (session != null) {
                disposeSession(session);
                cleaned++;
            }
        }
        
        log.info("KieSession池销毁完成，清理了{}个session", cleaned);
    }

    /**
     * 池统计信息（增强版）
     */
    public static class PoolStats {
        private final int activeCount;
        private final int idleCount;
        private final int totalCreated;
        private final int maxPoolSize;
        private final int failedValidations;
        private final boolean isShutdown;

        public PoolStats(int activeCount, int idleCount, int totalCreated, 
                        int maxPoolSize, int failedValidations, boolean isShutdown) {
            this.activeCount = activeCount;
            this.idleCount = idleCount;
            this.totalCreated = totalCreated;
            this.maxPoolSize = maxPoolSize;
            this.failedValidations = failedValidations;
            this.isShutdown = isShutdown;
        }

        public int getActiveCount() { return activeCount; }
        public int getIdleCount() { return idleCount; }
        public int getTotalCreated() { return totalCreated; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public int getFailedValidations() { return failedValidations; }
        public boolean isShutdown() { return isShutdown; }
        
        public double getUtilization() { 
            return maxPoolSize > 0 ? (double) activeCount / maxPoolSize : 0.0; 
        }
        
        public double getHealthScore() {
            // 计算健康评分（0-100）
            double utilizationScore = Math.min(getUtilization() * 100, 100);
            double validationScore = totalCreated > 0 ? 
                (1.0 - (double) failedValidations / totalCreated) * 100 : 100;
            return (utilizationScore + validationScore) / 2;
        }

        @Override
        public String toString() {
            return String.format(
                "PoolStats{active=%d, idle=%d, total=%d, max=%d, failed=%d, " +
                "utilization=%.2f%%, health=%.2f%%, shutdown=%s}", 
                activeCount, idleCount, totalCreated, maxPoolSize, failedValidations,
                getUtilization() * 100, getHealthScore(), isShutdown);
        }
    }
}
