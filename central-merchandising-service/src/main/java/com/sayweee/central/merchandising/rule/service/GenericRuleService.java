package com.sayweee.central.merchandising.rule.service;

import com.sayweee.central.merchandising.rule.entity.RuleExecution;
import com.sayweee.central.merchandising.rule.exception.RuleEngineException;
import com.sayweee.central.merchandising.rule.factory.RuleExecutionFactory;
import com.sayweee.central.merchandising.rule.model.RuleExecutionRequest;
import com.sayweee.central.merchandising.rule.model.RuleExecutionResponse;
import com.sayweee.central.merchandising.rule.strategy.RuleExecutionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> lifei
 * @Classname GenericRuleService
 * @Description TODO
 * @Date 2025/5/15 18:13
 */
@Service
@Slf4j
public class GenericRuleService {



    @Resource
    private DroolsConfig droolsConfig;

    @Resource
    private RuleExecutionLogService ruleExecutionLogService;

    /**
     * 执行规则
     */
    public RuleExecutionResponse executeRules(RuleExecutionRequest request) {
        // 创建响应对象
        RuleExecutionResponse response = new RuleExecutionResponse();
        response.setRequestId(request.getRequestId());

        // 记录规则触发数量
        AtomicInteger rulesFiredCount = new AtomicInteger(0);

        // 规则计时器
        Map<String, Long> ruleStartTimes = new HashMap<>();

        KieSession kieSession = null;
        long startTime = System.currentTimeMillis();

        try {
            // 获取KieSession
            kieSession = droolsConfig.kieSession();

            if (kieSession == null) {
                throw new RuleEngineException("Failed to create rule session for ruleset: " + request.getRulesetId());
            }

            // 创建结果收集器
            Map<String, Object> resultsCollector = new HashMap<>();

            // 注册全局变量
            kieSession.setGlobal("resultsCollector", resultsCollector);

            // 注册规则执行监听器，用于记录规则执行和性能
            kieSession.addEventListener(new org.kie.api.event.rule.DefaultAgendaEventListener() {
                @Override
                public void beforeMatchFired(org.kie.api.event.rule.BeforeMatchFiredEvent event) {
                    String ruleName = event.getMatch().getRule().getName();
                    ruleStartTimes.put(ruleName, System.currentTimeMillis());
                    log.debug("Rule about to fire: {}", ruleName);
                }

                @Override
                public void afterMatchFired(org.kie.api.event.rule.AfterMatchFiredEvent event) {
                    String ruleName = event.getMatch().getRule().getName();
                    long endTime = System.currentTimeMillis();
                    long startTime = ruleStartTimes.getOrDefault(ruleName, endTime);
                    long duration = endTime - startTime;

                    // 如果结果收集器中已有该规则的结果对象，更新执行时间
                    if (resultsCollector.get(ruleName) instanceof GenericRuleResult) {
                        GenericRuleResult result = (GenericRuleResult) resultsCollector.get(ruleName);
                        result.setExecutionTimeMillis(duration);
                        result.setFired(true);
                    }

                    rulesFiredCount.incrementAndGet();
                    log.debug("Rule fired: {}, duration: {} ms", ruleName, duration);
                }
            });

            // 插入请求对象
            kieSession.insert(request);

            // 从上下文中提取数据并插入
            for (Map.Entry<String, Object> entry : request.getContext().entrySet()) {
                kieSession.insert(entry.getValue());
            }

            // 执行规则流程
            executeRuleFlow(kieSession, request);

            // 记录触发的规则数量
            response.setRulesExecutedCount(rulesFiredCount.get());

            // 从结果收集器中获取结果
            response.setRuleResults(resultsCollector);

            // 如果有聚合结论
            if (resultsCollector.containsKey("conclusion")) {
                response.setConclusion(resultsCollector.get("conclusion"));
            }

            response.setStatus("SUCCESS");
        } catch (Exception e) {
            log.error("Error executing rules: ", e);
            response.setStatus("ERROR");
            response.setErrorMessage("Error executing rules: " + e.getMessage());
        } finally {
            // 计算总执行时间
            long endTime = System.currentTimeMillis();
            response.setExecutionTimeMillis(endTime - startTime);

            // 释放KieSession资源
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        // 记录规则执行日志
        if (request.getConfig() != null && request.getConfig().isLogRuleExecution()) {
            try {
                RuleExecution execution = ruleExecutionLogService.logRuleExecution(request, response);

                // 如果需要记录每条规则的执行情况
                if (execution != null && request.getConfig().isLogIndividualRules()) {
                    ruleExecutionLogService.logRuleExecutionDetails(execution, response.getRuleResults());
                }
            } catch (Exception e) {
                log.error("Failed to log rule execution: {}", e.getMessage());
            }
        }

        return response;
    }

    /**
     * 执行规则流程
     */
    private void executeRuleFlow(KieSession kieSession, RuleExecutionRequest request) {
        // 如果配置了特定的议程组
        if (request.getConfig() != null && request.getConfig().getAgendaGroups() != null
            && !request.getConfig().getAgendaGroups().isEmpty()) {
            // 按顺序执行指定的议程组
            for (String agendaGroup : request.getConfig().getAgendaGroups()) {
                log.debug("Setting focus to agenda group: {}", agendaGroup);
                kieSession.getAgenda().getAgendaGroup(agendaGroup).setFocus();
                kieSession.fireAllRules();
            }
        } else {
            // 执行所有规则
            log.debug("Firing all rules without specific agenda groups");
            kieSession.fireAllRules();
        }

        // 如果启用了聚合规则
        if (request.getConfig() != null && request.getConfig().isEnableAggregation()) {
            log.debug("Setting focus to aggregation agenda group");
            kieSession.getAgenda().getAgendaGroup("aggregation").setFocus();
            kieSession.fireAllRules();
        }
    }


}
