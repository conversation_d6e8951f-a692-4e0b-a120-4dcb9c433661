package com.sayweee.central.merchandising.rule.context;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.ArrayList;

import com.sayweee.central.merchandising.rule.exception.RuleContextValidationException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品验证规则上下文
 * <p>
 * 专门用于产品验证规则的类型安全上下文实现
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
@Slf4j
public class ProductValidationContext implements RuleContext {

    private static final String CONTEXT_TYPE = "product_validation";

    // 产品验证专用字段
    private Map<String, String> systemFields;
    private Map<String, String> aiFields;
    private List<String> ingredients;
    private Boolean hasProp65Label;
    private String productId;
    private String productName;
    private String category;
    private Double price;
    private String brand;
    private String description;

    // 扩展字段（用于兼容性）
    private Map<String, Object> additionalFields;

    /**
     * 构造函数
     */
    public ProductValidationContext() {
        this.systemFields = new HashMap<>();
        this.aiFields = new HashMap<>();
        this.ingredients = new ArrayList<>();
        this.additionalFields = new HashMap<>();
    }

    /**
     * 构造函数
     * 
     * @param systemFields 系统字段
     * @param aiFields AI字段
     */
    public ProductValidationContext(Map<String, String> systemFields, Map<String, String> aiFields) {
        this();
        this.systemFields = systemFields != null ? new HashMap<>(systemFields) : new HashMap<>();
        this.aiFields = aiFields != null ? new HashMap<>(aiFields) : new HashMap<>();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        Object value = getFieldValue(key);
        if (value == null) {
            return null;
        }

        try {
            if (type.isInstance(value)) {
                return (T) value;
            } else {
                log.warn("产品验证上下文类型不匹配 - 键: {}, 期望类型: {}, 实际类型: {}", 
                        key, type.getSimpleName(), value.getClass().getSimpleName());
                return null;
            }
        } catch (ClassCastException e) {
            throw new RuleContextValidationException(key, type, value.getClass());
        }
    }

    @Override
    public Object get(String key) {
        return getFieldValue(key);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> void put(String key, T value) {
        if (key == null) {
            throw new IllegalArgumentException("键不能为空");
        }

        try {
            switch (key) {
                case "systemFields":
                    this.systemFields = (Map<String, String>) value;
                    break;
                case "aiFields":
                    this.aiFields = (Map<String, String>) value;
                    break;
                case "ingredients":
                    this.ingredients = (List<String>) value;
                    break;
                case "hasProp65Label":
                    this.hasProp65Label = (Boolean) value;
                    break;
                case "productId":
                    this.productId = (String) value;
                    break;
                case "productName":
                    this.productName = (String) value;
                    break;
                case "category":
                    this.category = (String) value;
                    break;
                case "price":
                    this.price = (Double) value;
                    break;
                case "brand":
                    this.brand = (String) value;
                    break;
                case "description":
                    this.description = (String) value;
                    break;
                default:
                    // 存储到扩展字段中
                    if (value == null) {
                        additionalFields.remove(key);
                    } else {
                        additionalFields.put(key, value);
                    }
                    break;
            }
        } catch (ClassCastException e) {
            throw new RuleContextValidationException("设置字段 '" + key + "' 时类型转换失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean containsKey(String key) {
        return getFieldValue(key) != null || additionalFields.containsKey(key);
    }

    @Override
    public Set<String> keySet() {
        Set<String> keys = Set.of(
            "systemFields", "aiFields", "ingredients", "hasProp65Label",
            "productId", "productName", "category", "price", "brand", "description"
        );
        
        Set<String> allKeys = new HashMap<String, Object>() {{
            putAll(additionalFields);
            for (String key : keys) {
                if (getFieldValue(key) != null) {
                    put(key, true);
                }
            }
        }}.keySet();
        
        return allKeys;
    }

    @Override
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        
        // 添加标准字段
        if (systemFields != null) map.put("systemFields", systemFields);
        if (aiFields != null) map.put("aiFields", aiFields);
        if (ingredients != null) map.put("ingredients", ingredients);
        if (hasProp65Label != null) map.put("hasProp65Label", hasProp65Label);
        if (productId != null) map.put("productId", productId);
        if (productName != null) map.put("productName", productName);
        if (category != null) map.put("category", category);
        if (price != null) map.put("price", price);
        if (brand != null) map.put("brand", brand);
        if (description != null) map.put("description", description);
        
        // 添加扩展字段
        map.putAll(additionalFields);
        
        return map;
    }

    @Override
    public void validate() throws RuleContextValidationException {
        List<String> errors = new ArrayList<>();

        // 验证必需字段
        if (systemFields == null || systemFields.isEmpty()) {
            errors.add("systemFields不能为空");
        }

        if (aiFields == null || aiFields.isEmpty()) {
            errors.add("aiFields不能为空");
        }

        // 验证产品基本信息
        if (productId == null || productId.trim().isEmpty()) {
            errors.add("productId不能为空");
        }

        // 验证价格
        if (price != null && price < 0) {
            errors.add("price不能为负数");
        }

        // 验证成分列表
        if (ingredients != null) {
            for (int i = 0; i < ingredients.size(); i++) {
                String ingredient = ingredients.get(i);
                if (ingredient == null || ingredient.trim().isEmpty()) {
                    errors.add("ingredients[" + i + "]不能为空");
                }
            }
        }

        if (!errors.isEmpty()) {
            throw new RuleContextValidationException("产品验证上下文验证失败", errors);
        }

        log.debug("产品验证上下文验证通过 - 产品ID: {}, 字段数: {}", productId, keySet().size());
    }

    @Override
    public String getContextType() {
        return CONTEXT_TYPE;
    }

    @Override
    public void clear() {
        systemFields.clear();
        aiFields.clear();
        ingredients.clear();
        additionalFields.clear();
        hasProp65Label = null;
        productId = null;
        productName = null;
        category = null;
        price = null;
        brand = null;
        description = null;
    }

    @Override
    public int size() {
        return toMap().size();
    }

    @Override
    public boolean isEmpty() {
        return systemFields.isEmpty() && aiFields.isEmpty() && 
               ingredients.isEmpty() && additionalFields.isEmpty() &&
               hasProp65Label == null && productId == null && 
               productName == null && category == null && 
               price == null && brand == null && description == null;
    }

    @Override
    public void merge(RuleContext other) {
        if (other == null) {
            return;
        }

        Map<String, Object> otherData = other.toMap();
        for (Map.Entry<String, Object> entry : otherData.entrySet()) {
            put(entry.getKey(), entry.getValue());
        }

        log.debug("产品验证上下文合并完成，当前大小: {}", size());
    }

    @Override
    public RuleContext copy() {
        ProductValidationContext copy = new ProductValidationContext();
        copy.systemFields = systemFields != null ? new HashMap<>(systemFields) : new HashMap<>();
        copy.aiFields = aiFields != null ? new HashMap<>(aiFields) : new HashMap<>();
        copy.ingredients = ingredients != null ? new ArrayList<>(ingredients) : new ArrayList<>();
        copy.additionalFields = new HashMap<>(additionalFields);
        copy.hasProp65Label = hasProp65Label;
        copy.productId = productId;
        copy.productName = productName;
        copy.category = category;
        copy.price = price;
        copy.brand = brand;
        copy.description = description;
        return copy;
    }

    // 私有方法

    private Object getFieldValue(String key) {
        switch (key) {
            case "systemFields": return systemFields;
            case "aiFields": return aiFields;
            case "ingredients": return ingredients;
            case "hasProp65Label": return hasProp65Label;
            case "productId": return productId;
            case "productName": return productName;
            case "category": return category;
            case "price": return price;
            case "brand": return brand;
            case "description": return description;
            default: return additionalFields.get(key);
        }
    }

    // 便利方法

    /**
     * 添加成分
     */
    public void addIngredient(String ingredient) {
        if (ingredient != null && !ingredient.trim().isEmpty()) {
            if (ingredients == null) {
                ingredients = new ArrayList<>();
            }
            ingredients.add(ingredient.trim());
        }
    }

    /**
     * 设置系统字段
     */
    public void setSystemField(String key, String value) {
        if (systemFields == null) {
            systemFields = new HashMap<>();
        }
        systemFields.put(key, value);
    }

    /**
     * 设置AI字段
     */
    public void setAiField(String key, String value) {
        if (aiFields == null) {
            aiFields = new HashMap<>();
        }
        aiFields.put(key, value);
    }

    /**
     * 获取系统字段
     */
    public String getSystemField(String key) {
        return systemFields != null ? systemFields.get(key) : null;
    }

    /**
     * 获取AI字段
     */
    public String getAiField(String key) {
        return aiFields != null ? aiFields.get(key) : null;
    }
}
