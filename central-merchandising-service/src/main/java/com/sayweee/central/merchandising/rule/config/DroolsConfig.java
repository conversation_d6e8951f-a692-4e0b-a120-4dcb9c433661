package com.sayweee.central.merchandising.rule.config;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.kie.api.KieServices;
import org.kie.api.builder.*;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.Assert;

import lombok.extern.slf4j.Slf4j;

/**
 * Drools规则引擎配置类（池化版本）
 */
@Slf4j
@Configuration
public class DroolsConfig {

    @Value("${drools.rules.path:rule/}")
    private String rulesPath;

    @Value("${drools.rules.encoding:UTF-8}")
    private String rulesEncoding;

    @Value("${drools.enabled:true}")
    private boolean droolsEnabled;

    private KieContainer kieContainer;
    private final Map<String, Resource> ruleResources = new HashMap<>();

    // 移除KieSessionPool的直接依赖，避免循环依赖

    @PostConstruct
    public void validateConfig() {
        Assert.hasText(rulesPath, "Drools rules path must not be empty");
        Assert.hasText(rulesEncoding, "Drools rules encoding must not be empty");
        log.info("Drools configuration validated successfully");
    }

    /**
     * 优化后的KieContainer创建方法
     * 正确加载所有规则文件，支持动态规则加载
     */
    @Bean
    public KieContainer kieContainer() throws IOException {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kfs = kieServices.newKieFileSystem();
        
        log.info("Starting to load rule files from path: {}", rulesPath);
        
        try {
            // 加载所有规则文件
            Resource[] ruleFiles = loadRuleFiles();
            
            if (ruleFiles.length == 0) {
                log.warn("No rule files found in path: {}, loading default rules", rulesPath);
                // 如果没有找到规则文件，加载默认规则
                loadDefaultRules(kfs);
            } else {
                // 加载实际的规则文件
                for (Resource resource : ruleFiles) {
                    String path = "src/main/resources/" + rulesPath + resource.getFilename();
                    log.info("Loading rule file: {} from path: {}", resource.getFilename(), path);
                    kfs.write(path, ResourceFactory.newClassPathResource(
                        rulesPath + resource.getFilename(), rulesEncoding));
                }
                
                // 缓存规则资源
                cacheRuleResources(ruleFiles);
            }
            
            // 构建KieModule
            KieBuilder kieBuilder = kieServices.newKieBuilder(kfs);
            kieBuilder.buildAll();
            
            // 检查构建结果
            Results results = kieBuilder.getResults();
            if (results.hasMessages(Message.Level.ERROR)) {
                StringBuilder errors = new StringBuilder("Rule compilation failed:\n");
                results.getMessages(Message.Level.ERROR).forEach(msg -> {
                    errors.append("  - ").append(msg.getText()).append("\n");
                    log.error("Rule compilation error: {}", msg.getText());
                });
                throw new RuntimeException(errors.toString());
            }
            
            if (results.hasMessages(Message.Level.WARNING)) {
                results.getMessages(Message.Level.WARNING).forEach(msg ->
                    log.warn("Rule compilation warning: {}", msg.getText()));
            }
            
            // 创建容器并保存引用
            this.kieContainer = kieServices.newKieContainer(
                kieServices.getRepository().getDefaultReleaseId());
            
            log.info("KieContainer created successfully with {} rule files", ruleFiles.length);
            return this.kieContainer;
            
        } catch (IOException e) {
            log.error("Failed to load rule files", e);
            throw new RuntimeException("Failed to load rule files from " + rulesPath, e);
        } catch (Exception e) {
            log.error("Failed to create KieContainer", e);
            throw new RuntimeException("Cannot create KieContainer", e);
        }
    }
    
    /**
     * 加载默认规则（当没有找到规则文件时使用）
     */
    private void loadDefaultRules(KieFileSystem kfs) {
        String defaultRule = "package com.sayweee.central.merchandising.rule\n" +
                           "\n" +
                           "rule \"default_pass\"\n" +
                           "    when\n" +
                           "        eval(true)\n" +
                           "    then\n" +
                           "        System.out.println(\"Default rule executed\");\n" +
                           "end";
        kfs.write("src/main/resources/rules/default.drl", defaultRule);
    }

    /**
     * 创建KieSession（供KieSessionPool使用）
     * 确保使用已初始化的容器实例
     */
    public KieSession createSession() {
        if (kieContainer == null) {
            throw new IllegalStateException("KieContainer not initialized");
        }
        
        try {
            KieSession session = kieContainer.newKieSession();
            if (session == null) {
                throw new RuntimeException("Failed to create KieSession from container");
            }
            return session;
        } catch (Exception e) {
            log.error("Failed to create KieSession", e);
            throw new RuntimeException("Cannot create KieSession", e);
        }
    }

    /**
     * 创建KieSession Bean
     * 每次注入时都会创建新的实例
     */
    @Bean
    @Scope("prototype")
    public KieSession kieSession() {
        return createSession();
    }
    
    /**
     * 获取已加载的规则资源
     */
    public Map<String, Resource> getRuleResources() {
        return new HashMap<>(ruleResources);
    }
    
    /**
     * 重新加载规则（支持热更新）
     */
    public synchronized void reloadRules() throws IOException {
        log.info("Reloading rules...");
        KieContainer oldContainer = this.kieContainer;
        
        try {
            // 创建新的容器
            this.kieContainer = kieContainer();
            
            // 清理旧容器
            if (oldContainer != null) {
                oldContainer.dispose();
            }
            
            log.info("Rules reloaded successfully");
        } catch (Exception e) {
            // 恢复旧容器
            this.kieContainer = oldContainer;
            log.error("Failed to reload rules, keeping old container", e);
            throw new RuntimeException("Failed to reload rules", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (kieContainer != null) {
            log.info("Disposing KieContainer");
            kieContainer.dispose();
        }
    }

    // 以下私有方法保持不变...
    private Resource[] loadRuleFiles() throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        String effectivePath = rulesPath.endsWith("/") ? rulesPath : rulesPath + "/";
        String pattern = "classpath*:" + effectivePath + "**/*.drl";
        return Arrays.stream(resolver.getResources(pattern))
            .filter(this::isValidResource)
            .toArray(Resource[]::new);
    }

    private boolean isValidResource(Resource resource) {
        try {
            return resource.exists() && resource.isReadable();
        } catch (Exception e) {
            log.warn("Invalid resource: {}", resource.getFilename(), e);
            return false;
        }
    }

    private void cacheRuleResources(Resource[] resources) {
        for (Resource resource : resources) {
            String filename = resource.getFilename();
            if (filename != null) {
                String rulesetId = filename.replace(".drl", "");
                ruleResources.put(rulesetId, resource);
            }
        }
    }

    private KieFileSystem createKieFileSystem(KieServices kieServices, Resource[] resources) {
        KieFileSystem kfs = kieServices.newKieFileSystem();
        for (Resource resource : resources) {
            String path = rulesPath + resource.getFilename();
            kfs.write(ResourceFactory.newClassPathResource(path, rulesEncoding));
        }
        return kfs;
    }

    private KieBuilder buildKieModule(KieServices kieServices, KieFileSystem kfs) {
        KieBuilder kieBuilder = kieServices.newKieBuilder(kfs);
        kieBuilder.buildAll();
        return kieBuilder;
    }

    private void validateBuildResults(Results results) {
        if (results.hasMessages(Message.Level.ERROR)) {
            results.getMessages(Message.Level.ERROR).forEach(msg ->
                log.error("Rule error: {}", msg.getText()));
        }
    }

    private KieContainer createEmptyContainer(KieServices kieServices) {
        try {
            // 创建一个最小的KieFileSystem来避免"Cannot find KieModule"错误
            KieFileSystem kfs = kieServices.newKieFileSystem();

            // 添加一个最小的规则文件
            String minimalRule = "package rules\n" +
                                "rule \"minimal\"\n" +
                                "when\n" +
                                "then\n" +
                                "end";
            kfs.write("src/main/resources/rules/minimal.drl", minimalRule);

            KieBuilder kieBuilder = kieServices.newKieBuilder(kfs);
            kieBuilder.buildAll();

            return kieServices.newKieContainer(kieServices.getRepository().getDefaultReleaseId());
        } catch (Exception e) {
            log.error("Failed to create empty container: {}", e.getMessage());
            throw new RuntimeException("Cannot create KieContainer", e);
        }
    }

    private KieServices getKieServices() {
        return KieServices.Factory.get();
    }
}
