package com.sayweee.central.merchandising.rule.context;

import java.util.Map;
import java.util.Set;

import com.sayweee.central.merchandising.rule.exception.RuleContextValidationException;

/**
 * 规则上下文接口
 * <p>
 * 提供类型安全的规则执行上下文，替代原有的 Map<String, Object>
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public interface RuleContext {

    /**
     * 获取指定类型的值
     * 
     * @param key 键名
     * @param type 期望的类型
     * @param <T> 泛型类型
     * @return 值，如果不存在或类型不匹配则返回null
     * @throws ClassCastException 如果类型转换失败
     */
    <T> T get(String key, Class<T> type);

    /**
     * 获取值（不进行类型检查）
     * 
     * @param key 键名
     * @return 值，如果不存在则返回null
     */
    Object get(String key);

    /**
     * 设置值
     * 
     * @param key 键名
     * @param value 值
     * @param <T> 值的类型
     */
    <T> void put(String key, T value);

    /**
     * 检查是否包含指定键
     * 
     * @param key 键名
     * @return 是否包含
     */
    boolean containsKey(String key);

    /**
     * 获取所有键
     * 
     * @return 键集合
     */
    Set<String> keySet();

    /**
     * 转换为Map格式（用于兼容性）
     * 
     * @return Map格式的上下文数据
     */
    Map<String, Object> toMap();

    /**
     * 验证上下文数据的完整性和正确性
     * 
     * @throws RuleContextValidationException 如果验证失败
     */
    void validate() throws RuleContextValidationException;

    /**
     * 获取上下文类型标识
     * 
     * @return 上下文类型
     */
    String getContextType();

    /**
     * 清空上下文
     */
    void clear();

    /**
     * 获取上下文大小
     * 
     * @return 键值对数量
     */
    int size();

    /**
     * 检查上下文是否为空
     * 
     * @return 是否为空
     */
    boolean isEmpty();

    /**
     * 合并另一个上下文
     * 
     * @param other 另一个上下文
     */
    void merge(RuleContext other);

    /**
     * 复制上下文
     * 
     * @return 上下文副本
     */
    RuleContext copy();
}
