package com.sayweee.central.merchandising.rule.context;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.sayweee.central.merchandising.rule.exception.RuleContextValidationException;
import lombok.extern.slf4j.Slf4j;

/**
 * 规则上下文工厂
 * <p>
 * 根据规则集ID创建对应的类型安全上下文
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Component
@Slf4j
public class RuleContextFactory {

    /**
     * 根据规则集ID创建上下文
     * 
     * @param rulesetId 规则集ID
     * @param contextData 上下文数据
     * @return 类型安全的规则上下文
     * @throws RuleContextValidationException 如果创建失败
     */
    public RuleContext createContext(String rulesetId, Map<String, Object> contextData) {
        if (rulesetId == null || rulesetId.trim().isEmpty()) {
            throw new IllegalArgumentException("规则集ID不能为空");
        }

        try {
            switch (rulesetId.toLowerCase()) {
                case "product_validation":
                    return createProductValidationContext(contextData);
                
                case "inventory_check":
                    return createInventoryCheckContext(contextData);
                
                case "pricing_validation":
                    return createPricingValidationContext(contextData);
                
                default:
                    log.debug("使用通用上下文处理规则集: {}", rulesetId);
                    return createGenericContext(contextData, rulesetId);
            }
        } catch (Exception e) {
            log.error("创建规则上下文失败 - 规则集: {}, 错误: {}", rulesetId, e.getMessage(), e);
            throw new RuleContextValidationException("创建规则上下文失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建产品验证上下文
     */
    @SuppressWarnings("unchecked")
    private ProductValidationContext createProductValidationContext(Map<String, Object> data) {
        ProductValidationContext context = new ProductValidationContext();
        
        if (data != null) {
            // 设置系统字段
            Object systemFields = data.get("systemFields");
            if (systemFields instanceof Map) {
                context.setSystemFields((Map<String, String>) systemFields);
            }
            
            // 设置AI字段
            Object aiFields = data.get("aiFields");
            if (aiFields instanceof Map) {
                context.setAiFields((Map<String, String>) aiFields);
            }
            
            // 设置成分列表
            Object ingredients = data.get("ingredients");
            if (ingredients instanceof List) {
                context.setIngredients((List<String>) ingredients);
            }
            
            // 设置Prop65标签
            Object hasProp65Label = data.get("hasProp65Label");
            if (hasProp65Label instanceof Boolean) {
                context.setHasProp65Label((Boolean) hasProp65Label);
            }
            
            // 设置产品基本信息
            setStringField(context, data, "productId");
            setStringField(context, data, "productName");
            setStringField(context, data, "category");
            setStringField(context, data, "brand");
            setStringField(context, data, "description");
            
            // 设置价格
            Object price = data.get("price");
            if (price instanceof Number) {
                context.setPrice(((Number) price).doubleValue());
            }
            
            // 设置其他字段
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                if (!isStandardField(key)) {
                    context.put(key, entry.getValue());
                }
            }
        }
        
        // 验证上下文
        context.validate();
        
        log.debug("创建产品验证上下文成功，包含 {} 个字段", context.size());
        return context;
    }

    /**
     * 创建库存检查上下文
     */
    private RuleContext createInventoryCheckContext(Map<String, Object> data) {
        // 暂时使用通用上下文，后续可以创建专用的库存检查上下文
        GenericRuleContext context = new GenericRuleContext(data, "inventory_check");
        context.validate();
        
        log.debug("创建库存检查上下文成功，包含 {} 个字段", context.size());
        return context;
    }

    /**
     * 创建价格验证上下文
     */
    private RuleContext createPricingValidationContext(Map<String, Object> data) {
        // 暂时使用通用上下文，后续可以创建专用的价格验证上下文
        GenericRuleContext context = new GenericRuleContext(data, "pricing_validation");
        context.validate();
        
        log.debug("创建价格验证上下文成功，包含 {} 个字段", context.size());
        return context;
    }

    /**
     * 创建通用上下文
     */
    private GenericRuleContext createGenericContext(Map<String, Object> data, String contextType) {
        GenericRuleContext context = new GenericRuleContext(data, contextType);
        context.validate();
        
        log.debug("创建通用上下文成功 - 类型: {}, 包含 {} 个字段", contextType, context.size());
        return context;
    }

    /**
     * 从现有上下文创建新上下文
     * 
     * @param rulesetId 规则集ID
     * @param existingContext 现有上下文
     * @return 新的类型安全上下文
     */
    public RuleContext createContext(String rulesetId, RuleContext existingContext) {
        if (existingContext == null) {
            return createContext(rulesetId, null);
        }
        
        return createContext(rulesetId, existingContext.toMap());
    }

    /**
     * 验证上下文是否适用于指定规则集
     * 
     * @param rulesetId 规则集ID
     * @param context 上下文
     * @return 是否适用
     */
    public boolean isContextCompatible(String rulesetId, RuleContext context) {
        if (context == null) {
            return false;
        }
        
        try {
            switch (rulesetId.toLowerCase()) {
                case "product_validation":
                    return context instanceof ProductValidationContext || 
                           "product_validation".equals(context.getContextType());
                
                case "inventory_check":
                    return "inventory_check".equals(context.getContextType()) ||
                           "generic".equals(context.getContextType());
                
                case "pricing_validation":
                    return "pricing_validation".equals(context.getContextType()) ||
                           "generic".equals(context.getContextType());
                
                default:
                    return true; // 通用上下文兼容所有规则集
            }
        } catch (Exception e) {
            log.warn("检查上下文兼容性时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取支持的规则集列表
     * 
     * @return 支持的规则集ID列表
     */
    public List<String> getSupportedRulesets() {
        return List.of(
            "product_validation",
            "inventory_check", 
            "pricing_validation"
        );
    }

    // 私有辅助方法

    private void setStringField(ProductValidationContext context, Map<String, Object> data, String fieldName) {
        Object value = data.get(fieldName);
        if (value instanceof String) {
            context.put(fieldName, value);
        }
    }

    private boolean isStandardField(String key) {
        return List.of(
            "systemFields", "aiFields", "ingredients", "hasProp65Label",
            "productId", "productName", "category", "price", "brand", "description"
        ).contains(key);
    }
}
