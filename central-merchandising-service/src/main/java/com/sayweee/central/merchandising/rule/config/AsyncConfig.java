package com.sayweee.central.merchandising.rule.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import lombok.extern.slf4j.Slf4j;

/**
 * 异步配置类
 * <p>
 * 为规则引擎提供异步执行支持，实现超时控制
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    @Value("${drools.async.core-pool-size:5}")
    private int corePoolSize;

    @Value("${drools.async.max-pool-size:20}")
    private int maxPoolSize;

    @Value("${drools.async.queue-capacity:100}")
    private int queueCapacity;

    @Value("${drools.async.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    /**
     * 规则执行器线程池
     */
    @Bean("ruleExecutorPool")
    public Executor ruleExecutorPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("rule-executor-");
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("规则执行器线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }
}
