package com.sayweee.central.merchandising.rule.exception;

import java.util.List;
import java.util.ArrayList;

/**
 * 规则上下文验证异常
 * <p>
 * 当规则上下文数据验证失败时抛出此异常
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class RuleContextValidationException extends RuleEngineException {

    private static final String DEFAULT_ERROR_CODE = "RULE_CONTEXT_VALIDATION_ERROR";
    
    private final List<String> validationErrors;

    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public RuleContextValidationException(String message) {
        super(DEFAULT_ERROR_CODE, message);
        this.validationErrors = new ArrayList<>();
    }

    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param validationErrors 验证错误列表
     */
    public RuleContextValidationException(String message, List<String> validationErrors) {
        super(DEFAULT_ERROR_CODE, message);
        this.validationErrors = new ArrayList<>(validationErrors);
    }

    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public RuleContextValidationException(String message, Throwable cause) {
        super(DEFAULT_ERROR_CODE, message, cause);
        this.validationErrors = new ArrayList<>();
    }

    /**
     * 构造函数
     * 
     * @param fieldName 字段名
     * @param expectedType 期望类型
     * @param actualType 实际类型
     */
    public RuleContextValidationException(String fieldName, Class<?> expectedType, Class<?> actualType) {
        super(DEFAULT_ERROR_CODE, String.format("字段 '%s' 类型不匹配，期望: %s，实际: %s", 
                fieldName, expectedType.getSimpleName(), actualType.getSimpleName()));
        this.validationErrors = new ArrayList<>();
    }

    /**
     * 获取验证错误列表
     * 
     * @return 验证错误列表
     */
    public List<String> getValidationErrors() {
        return new ArrayList<>(validationErrors);
    }

    /**
     * 添加验证错误
     * 
     * @param error 错误信息
     */
    public void addValidationError(String error) {
        this.validationErrors.add(error);
    }

    /**
     * 检查是否有验证错误
     * 
     * @return 是否有错误
     */
    public boolean hasValidationErrors() {
        return !validationErrors.isEmpty();
    }

    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(super.getMessage());
        
        if (!validationErrors.isEmpty()) {
            sb.append(" 详细错误: [");
            sb.append(String.join(", ", validationErrors));
            sb.append("]");
        }
        
        return sb.toString();
    }
}
