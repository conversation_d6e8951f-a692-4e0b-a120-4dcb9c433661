package com.sayweee.central.merchandising.rule.monitor;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sayweee.central.merchandising.rule.pool.KieSessionPool;
import lombok.extern.slf4j.Slf4j;

/**
 * 规则引擎监控器
 * <p>
 * 提供规则引擎运行状态监控和统计信息
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Component
@Slf4j
public class RuleEngineMonitor {

    @Autowired
    private KieSessionPool kieSessionPool;

    // 统计计数器
    private final LongAdder totalExecutions = new LongAdder();
    private final LongAdder successfulExecutions = new LongAdder();
    private final LongAdder failedExecutions = new LongAdder();
    private final LongAdder timeoutExecutions = new LongAdder();
    
    // 执行时间统计
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    private final AtomicLong maxExecutionTime = new AtomicLong(0);
    private final AtomicLong minExecutionTime = new AtomicLong(Long.MAX_VALUE);
    
    // 启动时间
    private final LocalDateTime startTime = LocalDateTime.now();

    /**
     * 记录规则执行开始
     */
    public void recordExecutionStart() {
        totalExecutions.increment();
    }

    /**
     * 记录规则执行成功
     */
    public void recordExecutionSuccess(long executionTimeMs) {
        successfulExecutions.increment();
        updateExecutionTime(executionTimeMs);
    }

    /**
     * 记录规则执行失败
     */
    public void recordExecutionFailure(long executionTimeMs) {
        failedExecutions.increment();
        updateExecutionTime(executionTimeMs);
    }

    /**
     * 记录规则执行超时
     */
    public void recordExecutionTimeout(long executionTimeMs) {
        timeoutExecutions.increment();
        updateExecutionTime(executionTimeMs);
    }

    /**
     * 更新执行时间统计
     */
    private void updateExecutionTime(long executionTimeMs) {
        totalExecutionTime.addAndGet(executionTimeMs);
        
        // 更新最大执行时间
        long currentMax = maxExecutionTime.get();
        while (executionTimeMs > currentMax) {
            if (maxExecutionTime.compareAndSet(currentMax, executionTimeMs)) {
                break;
            }
            currentMax = maxExecutionTime.get();
        }
        
        // 更新最小执行时间
        long currentMin = minExecutionTime.get();
        while (executionTimeMs < currentMin) {
            if (minExecutionTime.compareAndSet(currentMin, executionTimeMs)) {
                break;
            }
            currentMin = minExecutionTime.get();
        }
    }

    /**
     * 获取监控统计信息
     */
    public MonitorStats getStats() {
        long total = totalExecutions.sum();
        long successful = successfulExecutions.sum();
        long failed = failedExecutions.sum();
        long timeout = timeoutExecutions.sum();
        
        double successRate = total > 0 ? (double) successful / total * 100 : 0.0;
        double failureRate = total > 0 ? (double) failed / total * 100 : 0.0;
        double timeoutRate = total > 0 ? (double) timeout / total * 100 : 0.0;
        
        long avgExecutionTime = total > 0 ? totalExecutionTime.get() / total : 0;
        long maxTime = maxExecutionTime.get() == 0 ? 0 : maxExecutionTime.get();
        long minTime = minExecutionTime.get() == Long.MAX_VALUE ? 0 : minExecutionTime.get();

        return MonitorStats.builder()
                .startTime(startTime)
                .totalExecutions(total)
                .successfulExecutions(successful)
                .failedExecutions(failed)
                .timeoutExecutions(timeout)
                .successRate(successRate)
                .failureRate(failureRate)
                .timeoutRate(timeoutRate)
                .avgExecutionTimeMs(avgExecutionTime)
                .maxExecutionTimeMs(maxTime)
                .minExecutionTimeMs(minTime)
                .poolStats(kieSessionPool.getPoolStats())
                .build();
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalExecutions.reset();
        successfulExecutions.reset();
        failedExecutions.reset();
        timeoutExecutions.reset();
        totalExecutionTime.set(0);
        maxExecutionTime.set(0);
        minExecutionTime.set(Long.MAX_VALUE);
        
        log.info("规则引擎监控统计信息已重置");
    }

    /**
     * 监控统计信息
     */
    public static class MonitorStats {
        private final LocalDateTime startTime;
        private final long totalExecutions;
        private final long successfulExecutions;
        private final long failedExecutions;
        private final long timeoutExecutions;
        private final double successRate;
        private final double failureRate;
        private final double timeoutRate;
        private final long avgExecutionTimeMs;
        private final long maxExecutionTimeMs;
        private final long minExecutionTimeMs;
        private final KieSessionPool.PoolStats poolStats;

        private MonitorStats(Builder builder) {
            this.startTime = builder.startTime;
            this.totalExecutions = builder.totalExecutions;
            this.successfulExecutions = builder.successfulExecutions;
            this.failedExecutions = builder.failedExecutions;
            this.timeoutExecutions = builder.timeoutExecutions;
            this.successRate = builder.successRate;
            this.failureRate = builder.failureRate;
            this.timeoutRate = builder.timeoutRate;
            this.avgExecutionTimeMs = builder.avgExecutionTimeMs;
            this.maxExecutionTimeMs = builder.maxExecutionTimeMs;
            this.minExecutionTimeMs = builder.minExecutionTimeMs;
            this.poolStats = builder.poolStats;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public LocalDateTime getStartTime() { return startTime; }
        public long getTotalExecutions() { return totalExecutions; }
        public long getSuccessfulExecutions() { return successfulExecutions; }
        public long getFailedExecutions() { return failedExecutions; }
        public long getTimeoutExecutions() { return timeoutExecutions; }
        public double getSuccessRate() { return successRate; }
        public double getFailureRate() { return failureRate; }
        public double getTimeoutRate() { return timeoutRate; }
        public long getAvgExecutionTimeMs() { return avgExecutionTimeMs; }
        public long getMaxExecutionTimeMs() { return maxExecutionTimeMs; }
        public long getMinExecutionTimeMs() { return minExecutionTimeMs; }
        public KieSessionPool.PoolStats getPoolStats() { return poolStats; }

        public static class Builder {
            private LocalDateTime startTime;
            private long totalExecutions;
            private long successfulExecutions;
            private long failedExecutions;
            private long timeoutExecutions;
            private double successRate;
            private double failureRate;
            private double timeoutRate;
            private long avgExecutionTimeMs;
            private long maxExecutionTimeMs;
            private long minExecutionTimeMs;
            private KieSessionPool.PoolStats poolStats;

            public Builder startTime(LocalDateTime startTime) { this.startTime = startTime; return this; }
            public Builder totalExecutions(long totalExecutions) { this.totalExecutions = totalExecutions; return this; }
            public Builder successfulExecutions(long successfulExecutions) { this.successfulExecutions = successfulExecutions; return this; }
            public Builder failedExecutions(long failedExecutions) { this.failedExecutions = failedExecutions; return this; }
            public Builder timeoutExecutions(long timeoutExecutions) { this.timeoutExecutions = timeoutExecutions; return this; }
            public Builder successRate(double successRate) { this.successRate = successRate; return this; }
            public Builder failureRate(double failureRate) { this.failureRate = failureRate; return this; }
            public Builder timeoutRate(double timeoutRate) { this.timeoutRate = timeoutRate; return this; }
            public Builder avgExecutionTimeMs(long avgExecutionTimeMs) { this.avgExecutionTimeMs = avgExecutionTimeMs; return this; }
            public Builder maxExecutionTimeMs(long maxExecutionTimeMs) { this.maxExecutionTimeMs = maxExecutionTimeMs; return this; }
            public Builder minExecutionTimeMs(long minExecutionTimeMs) { this.minExecutionTimeMs = minExecutionTimeMs; return this; }
            public Builder poolStats(KieSessionPool.PoolStats poolStats) { this.poolStats = poolStats; return this; }

            public MonitorStats build() {
                return new MonitorStats(this);
            }
        }
    }
}
