package com.sayweee.central.merchandising.rule.strategy;

import com.sayweee.central.merchandising.rule.exception.RuleEngineException;
import com.sayweee.central.merchandising.rule.model.GenericRuleResult;
import com.sayweee.central.merchandising.rule.model.RuleExecutionRequest;
import com.sayweee.central.merchandising.rule.model.RuleExecutionResponse;
import com.sayweee.central.merchandising.rule.pool.KieSessionPool;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 产品验证规则执行策略
 * 专门处理产品验证相关的规则
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
@Component
@Slf4j
public class ProductValidationStrategy implements RuleExecutionStrategy {

    @Autowired
    private KieSessionPool kieSessionPool;

    @Override
    public boolean supports(String rulesetId) {
        return "product_validation".equals(rulesetId);
    }

    @Override
    public String getStrategyName() {
        return "ProductValidation";
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public RuleExecutionResponse execute(RuleExecutionRequest request) {
        log.info("开始执行产品验证规则，请求ID: {}", request.getRequestId());
        
        long startTime = System.currentTimeMillis();
        KieSession kieSession = null;
        
        try {
            // 验证请求参数
            validateRequest(request);
            
            // 从池中获取KieSession
            kieSession = kieSessionPool.borrowSession();
            if (kieSession == null) {
                throw new RuleEngineException("无法获取KieSession，池可能已满");
            }
            
            // 设置全局变量用于收集结果
            Map<String, Object> resultsCollector = new HashMap<>();
            kieSession.setGlobal("resultsCollector", resultsCollector);
            
            // 插入请求对象到工作内存
            kieSession.insert(request);
            
            // 设置agenda group并执行规则
            kieSession.getAgenda().getAgendaGroup("product-validation").setFocus();
            int firedRules = kieSession.fireAllRules();
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 构建响应
            RuleExecutionResponse response = buildResponse(request, resultsCollector, firedRules, executionTime);
            
            log.info("产品验证规则执行完成，请求ID: {}, 触发规则数: {}, 耗时: {}ms", 
                    request.getRequestId(), firedRules, executionTime);
            
            return response;
            
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("产品验证规则执行失败，请求ID: {}, 耗时: {}ms", request.getRequestId(), executionTime, e);
            
            return buildErrorResponse(request, e, executionTime);
            
        } finally {
            // 归还KieSession到池中
            if (kieSession != null) {
                kieSessionPool.returnSession(kieSession);
            }
        }
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(RuleExecutionRequest request) {
        if (request.getContext() == null || request.getContext().isEmpty()) {
            throw new RuleEngineException("产品验证规则需要上下文数据");
        }
        
        Map<String, Object> context = request.getContext();
        
        // 验证必需的字段
        if (!context.containsKey("systemFields")) {
            throw new RuleEngineException("缺少systemFields字段");
        }
        
        if (!context.containsKey("aiFields")) {
            throw new RuleEngineException("缺少aiFields字段");
        }
    }

    /**
     * 构建成功响应
     */
    private RuleExecutionResponse buildResponse(RuleExecutionRequest request, 
                                              Map<String, Object> resultsCollector, 
                                              int firedRules, 
                                              long executionTime) {
        RuleExecutionResponse response = new RuleExecutionResponse();
        response.setRequestId(request.getRequestId());
        response.setRulesetId(request.getRulesetId());
        response.setBusinessKey(request.getBusinessKey());
        response.setStatus("SUCCESS");
        response.setExecutionTimeMillis(executionTime);
        response.setRulesExecutedCount(firedRules);
        response.setExecutionTime(LocalDateTime.now());
        
        // 设置规则结果
        response.setRuleResults(resultsCollector);
        
        // 生成结论
        String conclusion = generateConclusion(resultsCollector);
        response.setConclusion(conclusion);
        
        return response;
    }

    /**
     * 构建错误响应
     */
    private RuleExecutionResponse buildErrorResponse(RuleExecutionRequest request, 
                                                   Exception error, 
                                                   long executionTime) {
        RuleExecutionResponse response = new RuleExecutionResponse();
        response.setRequestId(request.getRequestId());
        response.setRulesetId(request.getRulesetId());
        response.setBusinessKey(request.getBusinessKey());
        response.setStatus("ERROR");
        response.setExecutionTimeMillis(executionTime);
        response.setRulesExecutedCount(0);
        response.setExecutionTime(LocalDateTime.now());
        response.setErrorMessage(error.getMessage());
        
        return response;
    }

    /**
     * 生成规则执行结论
     */
    private String generateConclusion(Map<String, Object> resultsCollector) {
        if (resultsCollector.isEmpty()) {
            return "无规则被触发";
        }
        
        // 统计不同结果类型的数量
        AtomicInteger passCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        AtomicInteger blockingCount = new AtomicInteger(0);
        
        resultsCollector.values().forEach(result -> {
            if (result instanceof GenericRuleResult) {
                GenericRuleResult ruleResult = (GenericRuleResult) result;
                String resultValue = ruleResult.getResult();
                String level = ruleResult.getLevel();
                
                if ("PASS".equals(resultValue)) {
                    passCount.incrementAndGet();
                } else if ("FAIL".equals(resultValue)) {
                    failCount.incrementAndGet();
                    if ("BLOCKING".equals(level)) {
                        blockingCount.incrementAndGet();
                    }
                }
            }
        });
        
        // 生成结论
        if (blockingCount.get() > 0) {
            return String.format("阻断性规则失败 %d 条，需要人工处理", blockingCount.get());
        } else if (failCount.get() > 0) {
            return String.format("规则检查失败 %d 条，通过 %d 条，建议人工审核", failCount.get(), passCount.get());
        } else {
            return String.format("所有规则检查通过，共 %d 条规则", passCount.get());
        }
    }
}
