package com.sayweee.central.merchandising.rule.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sayweee.central.merchandising.rule.monitor.RuleEngineMonitor;
import com.sayweee.central.merchandising.rule.pool.KieSessionPool;
import com.sayweee.core.framework.base.BaseResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 规则引擎监控控制器
 * <p>
 * 提供规则引擎运行状态监控和管理接口
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@RestController
@RequestMapping("/rule/monitor")
@Slf4j
public class RuleMonitorController {

    @Autowired
    private RuleEngineMonitor ruleEngineMonitor;

    @Autowired
    private KieSessionPool kieSessionPool;

    /**
     * 获取规则引擎监控统计信息
     */
    @GetMapping("/stats")
    public BaseResponse<RuleEngineMonitor.MonitorStats> getStats() {
        try {
            RuleEngineMonitor.MonitorStats stats = ruleEngineMonitor.getStats();
            return BaseResponse.success(stats);
        } catch (Exception e) {
            log.error("获取规则引擎监控统计信息失败", e);
            return BaseResponse.error("MONITOR_ERROR", "获取监控统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取 KieSession 池状态
     */
    @GetMapping("/pool")
    public BaseResponse<KieSessionPool.PoolStats> getPoolStats() {
        try {
            KieSessionPool.PoolStats poolStats = kieSessionPool.getPoolStats();
            return BaseResponse.success(poolStats);
        } catch (Exception e) {
            log.error("获取KieSession池状态失败", e);
            return BaseResponse.error("POOL_ERROR", "获取池状态失败: " + e.getMessage());
        }
    }

    /**
     * 重置监控统计信息
     */
    @PostMapping("/reset")
    public BaseResponse<Void> resetStats() {
        try {
            ruleEngineMonitor.resetStats();
            log.info("规则引擎监控统计信息已重置");
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("重置监控统计信息失败", e);
            return BaseResponse.error("RESET_ERROR", "重置统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理空闲的 KieSession
     */
    @PostMapping("/cleanup")
    public BaseResponse<Void> cleanupIdleSessions() {
        try {
            kieSessionPool.cleanupIdleSessions();
            log.info("空闲KieSession清理完成");
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("清理空闲KieSession失败", e);
            return BaseResponse.error("CLEANUP_ERROR", "清理失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public BaseResponse<HealthStatus> health() {
        try {
            KieSessionPool.PoolStats poolStats = kieSessionPool.getPoolStats();
            RuleEngineMonitor.MonitorStats monitorStats = ruleEngineMonitor.getStats();
            
            HealthStatus status = HealthStatus.builder()
                    .healthy(true)
                    .poolEnabled(poolStats.isPoolEnabled())
                    .totalSessions(poolStats.getTotalSessions())
                    .activeSessions(poolStats.getActiveSessions())
                    .availableSessions(poolStats.getAvailableSessions())
                    .totalExecutions(monitorStats.getTotalExecutions())
                    .successRate(monitorStats.getSuccessRate())
                    .avgExecutionTime(monitorStats.getAvgExecutionTimeMs())
                    .build();
            
            return BaseResponse.success(status);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            
            HealthStatus status = HealthStatus.builder()
                    .healthy(false)
                    .errorMessage(e.getMessage())
                    .build();
            
            return BaseResponse.success(status);
        }
    }

    /**
     * 健康状态信息
     */
    public static class HealthStatus {
        private final boolean healthy;
        private final boolean poolEnabled;
        private final int totalSessions;
        private final int activeSessions;
        private final int availableSessions;
        private final long totalExecutions;
        private final double successRate;
        private final long avgExecutionTime;
        private final String errorMessage;

        private HealthStatus(Builder builder) {
            this.healthy = builder.healthy;
            this.poolEnabled = builder.poolEnabled;
            this.totalSessions = builder.totalSessions;
            this.activeSessions = builder.activeSessions;
            this.availableSessions = builder.availableSessions;
            this.totalExecutions = builder.totalExecutions;
            this.successRate = builder.successRate;
            this.avgExecutionTime = builder.avgExecutionTime;
            this.errorMessage = builder.errorMessage;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public boolean isHealthy() { return healthy; }
        public boolean isPoolEnabled() { return poolEnabled; }
        public int getTotalSessions() { return totalSessions; }
        public int getActiveSessions() { return activeSessions; }
        public int getAvailableSessions() { return availableSessions; }
        public long getTotalExecutions() { return totalExecutions; }
        public double getSuccessRate() { return successRate; }
        public long getAvgExecutionTime() { return avgExecutionTime; }
        public String getErrorMessage() { return errorMessage; }

        public static class Builder {
            private boolean healthy;
            private boolean poolEnabled;
            private int totalSessions;
            private int activeSessions;
            private int availableSessions;
            private long totalExecutions;
            private double successRate;
            private long avgExecutionTime;
            private String errorMessage;

            public Builder healthy(boolean healthy) { this.healthy = healthy; return this; }
            public Builder poolEnabled(boolean poolEnabled) { this.poolEnabled = poolEnabled; return this; }
            public Builder totalSessions(int totalSessions) { this.totalSessions = totalSessions; return this; }
            public Builder activeSessions(int activeSessions) { this.activeSessions = activeSessions; return this; }
            public Builder availableSessions(int availableSessions) { this.availableSessions = availableSessions; return this; }
            public Builder totalExecutions(long totalExecutions) { this.totalExecutions = totalExecutions; return this; }
            public Builder successRate(double successRate) { this.successRate = successRate; return this; }
            public Builder avgExecutionTime(long avgExecutionTime) { this.avgExecutionTime = avgExecutionTime; return this; }
            public Builder errorMessage(String errorMessage) { this.errorMessage = errorMessage; return this; }

            public HealthStatus build() {
                return new HealthStatus(this);
            }
        }
    }
}
