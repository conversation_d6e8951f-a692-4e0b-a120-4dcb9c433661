package com.sayweee.central.merchandising.rule.exception.handler;

import com.sayweee.central.merchandising.rule.exception.RuleEngineException;
import com.sayweee.central.merchandising.rule.exception.RuleExecutionTimeoutException;
import com.sayweee.central.merchandising.rule.exception.RuleNotFoundException;
import com.sayweee.core.framework.base.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则引擎全局异常处理器
 * <p>
 * 统一处理规则引擎相关的异常，提供友好的错误响应
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@ControllerAdvice(annotations = RestController.class)
@Slf4j
@Order(1) // 高优先级，确保规则引擎异常优先被处理
public class RuleEngineExceptionHandler {

    /**
     * 处理规则未找到异常
     */
    @ExceptionHandler(RuleNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public BaseResponse<Void> handleRuleNotFound(RuleNotFoundException e) {
        log.warn("规则未找到异常: {}", e.getMessage());
        return BaseResponse.error(e.getErrorCode(), e.getMessage());
    }

    /**
     * 处理规则执行超时异常
     */
    @ExceptionHandler(RuleExecutionTimeoutException.class)
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public BaseResponse<Void> handleRuleExecutionTimeout(RuleExecutionTimeoutException e) {
        log.error("规则执行超时异常: {}", e.getMessage());
        return BaseResponse.error(e.getErrorCode(), e.getMessage());
    }

    /**
     * 处理规则引擎通用异常
     */
    @ExceptionHandler(RuleEngineException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public BaseResponse<Void> handleRuleEngineException(RuleEngineException e) {
        log.error("规则引擎执行异常: {}", e.getMessage(), e);
        
        String errorCode = e.getErrorCode() != null ? e.getErrorCode() : "RULE_ENGINE_ERROR";
        String message = e.getMessage() != null ? e.getMessage() : "规则引擎执行异常";
        
        return BaseResponse.error(errorCode, message);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public BaseResponse<Void> handleIllegalArgument(IllegalArgumentException e) {
        log.warn("规则引擎参数异常: {}", e.getMessage());
        return BaseResponse.error("RULE_INVALID_PARAMETER", "规则参数无效: " + e.getMessage());
    }

    /**
     * 处理状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public BaseResponse<Void> handleIllegalState(IllegalStateException e) {
        log.error("规则引擎状态异常: {}", e.getMessage());
        return BaseResponse.error("RULE_INVALID_STATE", "规则引擎状态异常: " + e.getMessage());
    }

    /**
     * 处理运行时异常（兜底处理）
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public BaseResponse<Void> handleRuntimeException(RuntimeException e) {
        // 如果是已知的规则引擎异常，不在这里处理
        if (e instanceof RuleEngineException) {
            throw e;
        }
        
        log.error("规则引擎未知运行时异常: {}", e.getMessage(), e);
        return BaseResponse.error("RULE_RUNTIME_ERROR", "规则引擎运行时异常");
    }

    /**
     * 处理通用异常（最后的兜底处理）
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public BaseResponse<Void> handleGeneralException(Exception e) {
        // 如果是已知异常，重新抛出让对应的处理器处理
        if (e instanceof RuleEngineException || e instanceof RuntimeException) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else {
                throw new RuntimeException(e);
            }
        }
        
        log.error("规则引擎未知异常: {}", e.getMessage(), e);
        return BaseResponse.error("RULE_UNKNOWN_ERROR", "规则引擎未知异常");
    }
}
