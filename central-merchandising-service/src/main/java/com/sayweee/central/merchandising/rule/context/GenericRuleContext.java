package com.sayweee.central.merchandising.rule.context;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.sayweee.central.merchandising.rule.exception.RuleContextValidationException;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用规则上下文实现
 * <p>
 * 提供基础的类型安全上下文实现，适用于大多数规则场景
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Slf4j
public class GenericRuleContext implements RuleContext {

    private final Map<String, Object> data;
    private final String contextType;

    /**
     * 构造函数
     */
    public GenericRuleContext() {
        this.data = new ConcurrentHashMap<>();
        this.contextType = "generic";
    }

    /**
     * 构造函数
     * 
     * @param initialData 初始数据
     */
    public GenericRuleContext(Map<String, Object> initialData) {
        this.data = new ConcurrentHashMap<>();
        this.contextType = "generic";
        
        if (initialData != null) {
            this.data.putAll(initialData);
        }
    }

    /**
     * 构造函数
     * 
     * @param contextType 上下文类型
     */
    public GenericRuleContext(String contextType) {
        this.data = new ConcurrentHashMap<>();
        this.contextType = contextType != null ? contextType : "generic";
    }

    /**
     * 构造函数
     * 
     * @param initialData 初始数据
     * @param contextType 上下文类型
     */
    public GenericRuleContext(Map<String, Object> initialData, String contextType) {
        this.data = new ConcurrentHashMap<>();
        this.contextType = contextType != null ? contextType : "generic";
        
        if (initialData != null) {
            this.data.putAll(initialData);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        if (key == null || type == null) {
            return null;
        }

        Object value = data.get(key);
        if (value == null) {
            return null;
        }

        try {
            if (type.isInstance(value)) {
                return (T) value;
            } else {
                log.warn("类型不匹配 - 键: {}, 期望类型: {}, 实际类型: {}", 
                        key, type.getSimpleName(), value.getClass().getSimpleName());
                return null;
            }
        } catch (ClassCastException e) {
            log.error("类型转换失败 - 键: {}, 期望类型: {}, 实际类型: {}", 
                     key, type.getSimpleName(), value.getClass().getSimpleName(), e);
            throw new RuleContextValidationException(key, type, value.getClass());
        }
    }

    @Override
    public Object get(String key) {
        return data.get(key);
    }

    @Override
    public <T> void put(String key, T value) {
        if (key == null) {
            throw new IllegalArgumentException("键不能为空");
        }
        
        if (value == null) {
            data.remove(key);
        } else {
            data.put(key, value);
        }
    }

    @Override
    public boolean containsKey(String key) {
        return data.containsKey(key);
    }

    @Override
    public Set<String> keySet() {
        return data.keySet();
    }

    @Override
    public Map<String, Object> toMap() {
        return new HashMap<>(data);
    }

    @Override
    public void validate() throws RuleContextValidationException {
        // 基础验证：检查是否有null键
        for (String key : data.keySet()) {
            if (key == null) {
                throw new RuleContextValidationException("上下文中不能包含null键");
            }
        }
        
        log.debug("通用规则上下文验证通过，包含 {} 个键值对", data.size());
    }

    @Override
    public String getContextType() {
        return contextType;
    }

    @Override
    public void clear() {
        data.clear();
    }

    @Override
    public int size() {
        return data.size();
    }

    @Override
    public boolean isEmpty() {
        return data.isEmpty();
    }

    @Override
    public void merge(RuleContext other) {
        if (other == null) {
            return;
        }
        
        Map<String, Object> otherData = other.toMap();
        data.putAll(otherData);
        
        log.debug("合并上下文完成，当前大小: {}", data.size());
    }

    @Override
    public RuleContext copy() {
        return new GenericRuleContext(new HashMap<>(data), contextType);
    }

    /**
     * 获取指定类型的值，如果不存在则返回默认值
     * 
     * @param key 键名
     * @param type 期望类型
     * @param defaultValue 默认值
     * @param <T> 泛型类型
     * @return 值或默认值
     */
    public <T> T getOrDefault(String key, Class<T> type, T defaultValue) {
        T value = get(key, type);
        return value != null ? value : defaultValue;
    }

    /**
     * 安全地设置值（如果值不为null）
     * 
     * @param key 键名
     * @param value 值
     * @param <T> 值类型
     */
    public <T> void putIfNotNull(String key, T value) {
        if (value != null) {
            put(key, value);
        }
    }

    /**
     * 批量设置值
     * 
     * @param values 键值对
     */
    public void putAll(Map<String, Object> values) {
        if (values != null) {
            data.putAll(values);
        }
    }

    /**
     * 移除指定键
     * 
     * @param key 键名
     * @return 被移除的值
     */
    public Object remove(String key) {
        return data.remove(key);
    }

    @Override
    public String toString() {
        return String.format("GenericRuleContext{type='%s', size=%d, keys=%s}", 
                           contextType, data.size(), data.keySet());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        GenericRuleContext that = (GenericRuleContext) obj;
        return data.equals(that.data) && contextType.equals(that.contextType);
    }

    @Override
    public int hashCode() {
        return data.hashCode() * 31 + contextType.hashCode();
    }
}
