package com.sayweee.central.merchandising.rule.strategy;

import com.sayweee.central.merchandising.rule.model.RuleExecutionRequest;
import com.sayweee.central.merchandising.rule.model.RuleExecutionResponse;

/**
 * 规则执行策略接口
 * 使用策略模式来处理不同类型的规则集
 *
 * <AUTHOR>
 * @since 2025/5/16
 */
public interface RuleExecutionStrategy {

    /**
     * 执行规则
     *
     * @param request 规则执行请求
     * @return 规则执行响应
     */
    RuleExecutionResponse execute(RuleExecutionRequest request);

    /**
     * 是否支持该规则集
     *
     * @param rulesetId 规则集ID
     * @return 是否支持
     */
    boolean supports(String rulesetId);

    /**
     * 获取策略名称
     *
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取策略优先级
     * 数值越小优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
